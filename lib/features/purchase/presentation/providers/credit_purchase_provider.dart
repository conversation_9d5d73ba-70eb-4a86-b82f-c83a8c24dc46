import 'dart:async';
import 'dart:io';

import 'package:flutter_audio_room/core/di/app_module.dart';
import 'package:flutter_audio_room/core/utils/loading_utils.dart';
import 'package:flutter_audio_room/core/utils/log_utils.dart';
import 'package:flutter_audio_room/features/authentication/presentation/providers/auth_providers.dart';
import 'package:flutter_audio_room/features/purchase/data/datasources/local_product_datasource.dart';
import 'package:flutter_audio_room/features/purchase/data/datasources/remote_product_datasource.dart';
import 'package:flutter_audio_room/features/purchase/data/repositories/product_repository_impl.dart';
import 'package:flutter_audio_room/features/purchase/data/repositories/purchase_repository_impl.dart';
import 'package:flutter_audio_room/features/purchase/domain/entities/purchase_item.dart';
import 'package:flutter_audio_room/features/purchase/domain/repositories/i_product_repository.dart';
import 'package:flutter_audio_room/features/purchase/domain/repositories/i_purchase_repository.dart';
import 'package:flutter_audio_room/features/purchase/presentation/providers/purchase_process_state_provider.dart';
import 'package:flutter_audio_room/shared/data/remote/network_service.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:in_app_purchase/in_app_purchase.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'credit_purchase_provider.g.dart';

@riverpod
class CreditPurchase extends _$CreditPurchase {
  StreamSubscription<PurchaseDetails>? _purchaseSubscription;
  IPurchaseRepository? _purchaseRepository;
  StateController<PurchaseProcessState>? _purchaseStateController;

  @override
  Future<List<PurchaseItem>> build() async {
    final purchaseRepository = _createPurchaseRepository();
    _purchaseRepository = purchaseRepository;

    _purchaseStateController = ref.read(purchaseProcessStateProvider.notifier);

    // 1. 设置购买状态监听
    _setupPurchaseListener();

    // 2. 清理资源
    ref.onDispose(() {
      _purchaseSubscription?.cancel();
      purchaseRepository.dispose();
    });

    // 3. 初始化仓库并获取产品
    return _initializeAndFetchProducts();
  }

  IProductRepository _createProductRepository() {
    final networkService = getIt<NetworkService>();
    final remoteDataSource = RemoteProductDatasource(networkService);
    final localDataSource = LocalProductDataSource();

    return ProductRepositoryImpl(
      localDataSource: localDataSource,
      remoteDataSource: remoteDataSource,
    );
  }

  PurchaseRepositoryImpl _createPurchaseRepository() {
    final inAppPurchase = InAppPurchase.instance;
    final productRepo = _createProductRepository();
    return PurchaseRepositoryImpl(
      inAppPurchase: inAppPurchase,
      productRepository: productRepo,
    );
  }

  /// 设置购买状态监听器
  void _setupPurchaseListener() {
    _purchaseSubscription =
        _purchaseRepository?.purchaseStatusStream.listen(_handlePurchaseUpdate);
  }

  /// 处理购买状态更新
  void _handlePurchaseUpdate(PurchaseDetails purchaseDetails) async {
    final products = await future;
    final productIds = products.map((e) => e.productId).toList();
    if (purchaseDetails.productID.isNotEmpty &&
        !productIds.contains(purchaseDetails.productID)) {
      return;
    }

    switch (purchaseDetails.status) {
      case PurchaseStatus.pending:
        // _setPurchaseState(PurchaseProcessState.purchasing);
        break;
      case PurchaseStatus.purchased:
        _setPurchaseState(PurchaseProcessState.purchased);
        await _handlePurchaseVerification(purchaseDetails);
        break;
      case PurchaseStatus.restored:
        _setPurchaseState(PurchaseProcessState.purchased);
        await _handlePurchaseVerification(purchaseDetails);
        break;
      case PurchaseStatus.error:
        _setPurchaseState(PurchaseProcessState.failed);
        LoadingUtils.showToast(purchaseDetails.error?.details ??
            'Purchase failed, please try again later');
        break;
      case PurchaseStatus.canceled:
        _setPurchaseState(PurchaseProcessState.canceled);
        break;
    }
  }

  /// 处理购买验证
  Future<void> _handlePurchaseVerification(
      PurchaseDetails purchaseDetails) async {
    try {
      _setPurchaseState(PurchaseProcessState.verifying);

      final result = await _purchaseRepository?.verifyPurchase(
        purchaseDetails,
        isSubscription: false,
      );

      result?.fold(
        (error) {
          _setPurchaseState(PurchaseProcessState.failed);
        },
        (data) {
          _setPurchaseState(PurchaseProcessState.verified);
        },
      );
    } catch (e) {
      _setPurchaseState(PurchaseProcessState.failed);
    }

    if (purchaseDetails.pendingCompletePurchase) {
      await _purchaseRepository?.completePurchase(purchaseDetails);
    }
  }

  /// 初始化仓库并获取产品列表
  Future<List<PurchaseItem>> _initializeAndFetchProducts() async {
    // 初始化仓库
    final result = await _purchaseRepository?.initialize();
    if (result == null) {
      throw Exception('Failed to initialize purchase repository');
    }

    return result.fold(
      (error) => throw error,
      (_) async {
        // 获取产品列表
        final productsResult = await _purchaseRepository
            ?.initProducts()
            .timeout(const Duration(seconds: 20));
        if (productsResult == null) {
          throw Exception('Failed to fetch products');
        }
        return productsResult.fold(
          (error) => throw error,
          (products) {
            products.sort((a, b) =>
                a.product.productOrder.compareTo(b.product.productOrder));
            return products;
          },
        );
      },
    );
  }
  /// 设置购买状态
  void _setPurchaseState(PurchaseProcessState newState) {
    _purchaseStateController?.state = newState;
  }

  /// 购买产品
  Future<void> purchaseProduct(String productId) async {
    if (purchaseState == PurchaseProcessState.purchasing) {
      return;
    }

    // 先获取所有 provider 引用
    final userIdFuture = ref.read(userIdProvider.future);
    final userUUIDFuture =
        Platform.isIOS ? ref.read(userUUIDProvider.future) : null;
    
    _setPurchaseState(PurchaseProcessState.purchasing);

    // 然后执行异步操作
    String? userId = await userIdFuture;
    
    if (Platform.isIOS && userUUIDFuture != null) {
      userId = await userUUIDFuture;
    }

    if (userId == null) {
      LoadingUtils.showToast('User UUID is null');
      _setPurchaseState(PurchaseProcessState.failed);
      return;
    }
    
    final result = await _purchaseRepository?.purchaseProduct(
      productId,
      isSubscription: false,
      userId: userId,
    );
    
    result?.fold(
      (error) {
        _setPurchaseState(PurchaseProcessState.failed);
        LogUtils.e('Failed to purchase product: ${error.message}',
            tag: 'CreditPurchase');
        LoadingUtils.showToast('Failed to purchase product');
      },
      (_) {},
    );
  }

  /// 恢复购买
  Future<void> restorePurchases() async {
    // 重置并设置状态
    resetPurchaseState();
    
    final result = await _purchaseRepository?.restorePurchases();

    result?.fold(
      (error) {
        _setPurchaseState(PurchaseProcessState.failed);
        LogUtils.e('Failed to restore purchases: ${error.message}',
            tag: 'CreditPurchase');
      },
      (items) {
        _handleRestoreResult(items);
      },
    );
  }
  
  /// 处理恢复结果
  void _handleRestoreResult(List<PurchaseItem> items) {
    // 如果没有可恢复的购买，也显示消息
    LogUtils.d('待恢复列表长度: ${items.length}', tag: 'CreditPurchase');
    LoadingUtils.showToast('No purchases found to restore');
  }

  /// 返回当前购买过程状态
  PurchaseProcessState get purchaseState =>
      ref.watch(purchaseProcessStateProvider);

  /// 检查是否正在购买中
  bool get isPurchasing =>
      purchaseState == PurchaseProcessState.purchasing ||
      purchaseState == PurchaseProcessState.verifying;

  /// 重置购买状态
  void resetPurchaseState() {
    _setPurchaseState(PurchaseProcessState.initial);
  }
}
