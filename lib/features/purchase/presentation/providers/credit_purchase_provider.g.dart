// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'credit_purchase_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$creditPurchaseHash() => r'4a9dfa07324dad185f94e7f5c34acd7b30254715';

/// See also [CreditPurchase].
@ProviderFor(CreditPurchase)
final creditPurchaseProvider = AutoDisposeAsyncNotifierProvider<CreditPurchase,
    List<PurchaseItem>>.internal(
  CreditPurchase.new,
  name: r'creditPurchaseProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$creditPurchaseHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$CreditPurchase = AutoDisposeAsyncNotifier<List<PurchaseItem>>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member
