import 'package:flutter/material.dart';
import 'package:flutter_audio_room/core/utils/loading_utils.dart';
import 'package:flutter_audio_room/core/widgets/app_button.dart';
import 'package:flutter_audio_room/features/purchase/presentation/providers/purchase_process_state_provider.dart';
import 'package:flutter_audio_room/features/purchase/presentation/providers/subscription_purchase_provider.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../widgets/subscription_purchase_item.dart';

class SubscriptionPurchasePage extends ConsumerWidget {
  const SubscriptionPurchasePage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final purchaseState = ref.watch(subscriptionPurchaseProvider);
    final processState = ref.watch(purchaseProcessStateProvider);

    // 监听购买状态变化
    ref.listen(purchaseProcessStateProvider, (previous, current) {
      if (previous != current && current == PurchaseProcessState.verified) {
        // 订阅验证成功，可以在这里执行额外操作
        LoadingUtils.showToast('Subscription successful!');
      } else if (previous != current &&
          current == PurchaseProcessState.failed) {
        // 订阅失败，可以显示重试按钮或其他UI
      } else if (previous != current &&
          current == PurchaseProcessState.canceled) {
        LoadingUtils.showToast('Subscription cancelled');
      }
    });

    // 判断按钮是否应该禁用
    bool isPurchasing() {
      return processState == PurchaseProcessState.purchasing ||
          processState == PurchaseProcessState.verifying;
    }

    return PopScope(
      canPop: !isPurchasing(),
      child: Stack(
        children: [
          Scaffold(
            appBar: AppBar(
              centerTitle: true,
              title: const Text('Premium Subscription'),
            ),
            body: Column(
              children: [
                Container(
                  padding: EdgeInsets.all(16.r),
                  color: Theme.of(context)
                      .colorScheme
                      .primary
                      .withValues(alpha: 0.1),
                  child: Column(
                    children: [
                      Text(
                        'Unlock Premium Features',
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                              fontWeight: FontWeight.bold,
                              color: Theme.of(context).colorScheme.primary,
                            ),
                      ),
                      16.verticalSpace,
                      _buildFeatureRow(context, 'Unlimited audio rooms'),
                      8.verticalSpace,
                      _buildFeatureRow(context, 'Higher quality audio'),
                      8.verticalSpace,
                      _buildFeatureRow(context, 'No advertisements'),
                      8.verticalSpace,
                      _buildFeatureRow(context, 'Priority customer support'),
                    ],
                  ),
                ),
                Expanded(
                  child: purchaseState.when(
                    data: (items) {
                      if (items.isEmpty) {
                        return const Center(
                          child: Text('No subscription plans available'),
                        );
                      }

                      return ListView.separated(
                        padding: EdgeInsets.all(16.w),
                        itemCount: items.length,
                        separatorBuilder: (context, index) => 16.verticalSpace,
                        itemBuilder: (context, index) {
                          final item = items[index];
                          final isPopular = item.product.isPopular == 1;
                          final isBestValue = item.product.bestValue == 1;

                          return SubscriptionPurchaseItem(
                            item: item,
                            isPopular: isPopular,
                            isBestValue: isBestValue,
                            onPurchase: () {
                              // 如果正在处理购买，禁止点击
                              if (isPurchasing()) {
                                LoadingUtils.showToast(
                                    'Processing, please wait');
                                return;
                              }
                              ref
                                  .read(subscriptionPurchaseProvider.notifier)
                                  .purchaseSubscription(
                                    item.productId,
                                  );
                            },
                            // 只有当前选中的产品才会显示特殊按钮状态
                            buttonText: _getButtonText(processState),
                            isDisabled: isPurchasing(),
                          );
                        },
                      );
                    },
                    loading: () =>
                        const Center(child: CircularProgressIndicator()),
                    error: (error, stack) => Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          const Text(
                            'It seems that there is an error, please try again later',
                            style: TextStyle(color: Colors.red),
                          ),
                          const SizedBox(height: 16),
                          ElevatedButton(
                            onPressed: () {
                              ref.invalidate(subscriptionPurchaseProvider);
                            },
                            child: const Text('Retry'),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
                SafeArea(
                  child: Column(
                    children: [
                      16.verticalSpace,
                      AppButton(
                        type: AppButtonType.text,
                        text: 'Restore Purchases',
                        textStyle: (context, defaultStyle) =>
                            defaultStyle.copyWith(
                          fontSize: 14.sp,
                          fontWeight: FontWeight.w400,
                        ),
                        onPressed: () {
                          // 如果正在处理购买，禁止点击
                          if (isPurchasing()) {
                            LoadingUtils.showToast('Processing, please wait');
                            return;
                          }
                          ref
                              .read(subscriptionPurchaseProvider.notifier)
                              .restorePurchases();
                        },
                      ),
                      8.verticalSpace,
                      Padding(
                        padding: EdgeInsets.symmetric(horizontal: 16.w),
                        child: Text(
                          'Payment will be charged to your Apple ID account at the confirmation of purchase. Subscription automatically renews unless it is canceled at least 24 hours before the end of the current period. Your account will be charged for renewal within 24 hours prior to the end of the current period. You can manage and cancel your subscriptions by going to your account settings on the App Store after purchase.',
                          style:
                              Theme.of(context).textTheme.bodySmall?.copyWith(
                                    color: Theme.of(context)
                                        .colorScheme
                                        .onSurface
                                        .withValues(alpha: 0.6),
                                  ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                      16.verticalSpace,
                    ],
                  ),
                ),
              ],
            ),
          ),

          // 显示全屏加载指示器
          if (isPurchasing())
            Container(
              color: Colors.black.withValues(alpha: 0.3),
              child: Center(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    const CircularProgressIndicator(),
                    const SizedBox(height: 16),
                    Text(
                      processState == PurchaseProcessState.purchasing
                          ? 'Processing subscription...'
                          : 'Verifying subscription...',
                      style: const TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildFeatureRow(BuildContext context, String feature) {
    return Row(
      children: [
        Icon(
          Icons.check_circle,
          color: Theme.of(context).colorScheme.primary,
          size: 20.r,
        ),
        8.horizontalSpace,
        Expanded(
          child: Text(
            feature,
            style: Theme.of(context).textTheme.bodyMedium,
          ),
        ),
      ],
    );
  }

  String _getButtonText(PurchaseProcessState state) {
    switch (state) {
      case PurchaseProcessState.purchasing:
        return 'Processing...';
      case PurchaseProcessState.verifying:
        return 'Verifying...';
      default:
        return 'Subscribe';
    }
  }
}
