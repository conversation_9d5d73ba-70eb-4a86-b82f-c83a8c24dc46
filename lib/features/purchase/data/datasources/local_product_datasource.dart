import 'package:flutter_audio_room/features/purchase/domain/models/product.dart';

class LocalProductDataSource {
  final List<Product> _products = [
    const Product(
      productId: 'gempack1',
      description: 'Buy 100 Credits',
      currencyCode: 'USD',
      price: 699,
      discountPrice: 699,
      discountRate: 0,
      gems: 100,
      productOrder: 1,
      productStatus: 1,
      isPopular: 0,
      bestValue: 0,
    ),
  ];
  
  final List<Product> _subscriptions = [
    const Product(
      subscriptionId: 'sub1',
      description: 'weekly',
      currencyCode: 'USD',
      price: 999,
      discountPrice: 999,
      discountRate: 0,
      gems: 0,
      productOrder: 1,
      productStatus: 1,
      isPopular: 1,
      bestValue: 0,
    ),
    const Product(
      subscriptionId: 'sub3',
      description: 'monthly',
      currencyCode: 'USD',
      price: 999,
      discountPrice: 999,
      discountRate: 0,
      gems: 0,
      productOrder: 1,
      productStatus: 1,
      isPopular: 1,
      bestValue: 0,
    ),
  ];

  List<Product> getProducts() => _products;
  
  List<Product> getSubscriptions() => _subscriptions;

  Product? getProduct(String id) {
    try {
      return _products.firstWhere((product) => product.productId == id);
    } catch (e) {
      // 如果在普通商品中找不到，则尝试在订阅中查找
      try {
        return _subscriptions
            .firstWhere((subscription) => subscription.productId == id);
      } catch (e) {
        return null;
      }
    }
  }
}
