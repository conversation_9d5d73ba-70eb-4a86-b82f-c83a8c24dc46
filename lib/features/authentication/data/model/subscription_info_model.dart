import 'package:freezed_annotation/freezed_annotation.dart';

part 'subscription_info_model.freezed.dart';
part 'subscription_info_model.g.dart';

@freezed
class SubscriptionInfoModel with _$SubscriptionInfoModel {
  @JsonSerializable(explicitToJson: true)
  const factory SubscriptionInfoModel({
    @Default(false) final bool subscribed,
    @Default('') final String expiresTime,
  }) = _SubscriptionInfoModel;

  factory SubscriptionInfoModel.fromJson(Map<String, dynamic> json) =>
      _$SubscriptionInfoModelFromJson(json);
}
