import 'package:flutter_audio_room/features/authentication/data/model/profile_model.dart';
import 'package:flutter_audio_room/features/authentication/data/model/subscription_info_model.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'personal_info_model.freezed.dart';
part 'personal_info_model.g.dart';

@freezed
class PersonalInfoModel with _$PersonalInfoModel {
  @JsonSerializable(explicitToJson: true)
  const factory PersonalInfoModel({
    @JsonKey(name: 'profileVO') final ProfileModel? profileVO,
    @Default(0) final int followeeCount,
    @Default(0) final int followerCount,
    @Default(0) final int mutualFollowCount,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'userSubscriptionInfo') final SubscriptionInfoModel? userSubscriptionInfo,
  }) = _PersonalInfoModel;

  factory PersonalInfoModel.fromJson(Map<String, dynamic> json) =>
      _$PersonalInfoModelFromJson(json);
}
