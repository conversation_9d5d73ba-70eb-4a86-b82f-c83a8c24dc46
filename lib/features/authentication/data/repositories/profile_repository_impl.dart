import 'package:flutter_audio_room/features/audio_room/data/model/other_user_info_model.dart';
import 'package:flutter_audio_room/features/authentication/data/model/avatar_frame_model.dart';
import 'package:flutter_audio_room/features/authentication/data/model/extra_user_info_model.dart';
import 'package:flutter_audio_room/features/authentication/data/model/personal_info_model.dart';
import 'package:flutter_audio_room/features/authentication/data/model/subscription_info_model.dart';
import 'package:flutter_audio_room/features/authentication/domain/repositories/profile_repository.dart';
import 'package:flutter_audio_room/shared/data/remote/network_service.dart';
import 'package:flutter_audio_room/shared/domain/models/either.dart';
import 'package:flutter_audio_room/shared/domain/types/common_types.dart';
import 'package:flutter_audio_room/shared/exceptions/error_handler.dart';

class ProfileRepositoryImpl implements IProfileRepository {
  final NetworkService _networkService;

  ProfileRepositoryImpl(this._networkService);

  @override
  Future<ResultWithData<ExtraUserInfoModel>> getExtraUserInfoById(
      String userId) async {
    final response = await _networkService.post(
      '/canary/user/info',
      data: {
        'userId': userId,
      },
    );

    return response.fold(
      (l) => Left(l),
      (r) => Right(ExtraUserInfoModel.fromJson(r)),
    );
  }

  @override
  Future<ResultWithData<OtherUserInfoModel>> getUserInfoById(
      String userId) async {
    final response = await _networkService.post(
      '/profile/profile/userBasicInfo',
      data: {
        'userId': userId,
      },
    );

    return response.fold(
      (l) => Left(l),
      (r) => Right(OtherUserInfoModel.fromJson(r)),
    );
  }

  @override
  Future<ResultWithData<AvatarFrameModel>> getCurrentFrame(
      String userId) async {
    final result =
        await _networkService.post('/canary/user/getAvatarFrame', data: {
      'userId': userId,
    });
    return result.fold(
      (l) => Left(l),
      (r) {
        try {
          final data = AvatarFrameModel.fromJson(r);
          return Right(data);
        } catch (e) {
          return Left(ErrorHandler.createValidationError(e.toString()));
        }
      },
    );
  }

  @override
  Future<ResultWithData<String>> getCurrentUserUUID() async {
    final response = await _networkService.get('/profile/profile/uuid');
    return response.fold(
      (l) => Left(l),
      (r) {
        final uuid = r['uuid'] as String?;
        if (uuid == null) {
          return Left(ErrorHandler.createValidationError('uuid is null'));
        }
        return Right(uuid);
      },
    );
  }

  @override
  Future<ResultWithData<SubscriptionInfoModel>> getSubscriptionInfo() async {
    final response =
        await _networkService.get('/profile/profile/subscriptionInfo');
    return response.fold(
      (l) => Left(l),
      (r) {
        try {
          final data = SubscriptionInfoModel.fromJson(r);
          return Right(data);
        } catch (e) {
          return Left(ErrorHandler.createValidationError(e.toString()));
        }
      },
    );
  }

  @override
  Future<ResultWithData<PersonalInfoModel>> getPersonalInfo() async {
    final response = await _networkService.get('/profile/profile/personalInfo');
    return response.fold(
      (l) => Left(l),
      (r) {
        try {
          final data = PersonalInfoModel.fromJson(r);
          return Right(data);
        } catch (e) {
          return Left(ErrorHandler.createValidationError(e.toString()));
        }
      },
    );
  }
}
