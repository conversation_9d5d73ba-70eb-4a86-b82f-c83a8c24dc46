// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'room_message_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$RoomMessageModelImpl _$$RoomMessageModelImplFromJson(
        Map<String, dynamic> json) =>
    _$RoomMessageModelImpl(
      id: json['id'] as String?,
      roomId: json['roomId'] as String?,
      createAt: (json['createAt'] as num?)?.toInt(),
      senderId: (json['senderId'] as num?)?.toInt(),
      sender: json['sender'] == null
          ? null
          : RoomUser.fromJson(json['sender'] as Map<String, dynamic>),
      event: $enumDecodeNullable(_$RoomMessageEventEnumMap, json['event']),
      eventSubtype: $enumDecodeNullable(
          _$RoomMessageEventSubtypeEnumMap, json['eventSubtype']),
      mention: json['mention'] == null
          ? null
          : RoomUser.fromJson(json['mention'] as Map<String, dynamic>),
      targetId: (json['targetId'] as num?)?.toInt(),
      targetUser: json['targetUser'] == null
          ? null
          : RoomUser.fromJson(json['targetUser'] as Map<String, dynamic>),
      followActionSource: $enumDecodeNullable(
          _$RoomMessageFollowActionSourceEnumMap, json['followActionSource']),
      extra: json['extra'] as String?,
      extraType:
          $enumDecodeNullable(_$RoomMessageExtraTypeEnumMap, json['extraType']),
      content: json['content'] as String?,
      position: json['position'] == null
          ? null
          : RoomPosition.fromJson(json['position'] as Map<String, dynamic>),
      gift: json['gift'] == null
          ? null
          : RoomMessageGift.fromJson(json['gift'] as Map<String, dynamic>),
      giftReceives: (json['giftReceives'] as List<dynamic>?)
          ?.map((e) => (e as num).toInt())
          .toList(),
    );

Map<String, dynamic> _$$RoomMessageModelImplToJson(
        _$RoomMessageModelImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'roomId': instance.roomId,
      'createAt': instance.createAt,
      'senderId': instance.senderId,
      'sender': instance.sender,
      'event': _$RoomMessageEventEnumMap[instance.event],
      'eventSubtype': _$RoomMessageEventSubtypeEnumMap[instance.eventSubtype],
      'mention': instance.mention,
      'targetId': instance.targetId,
      'targetUser': instance.targetUser,
      'followActionSource':
          _$RoomMessageFollowActionSourceEnumMap[instance.followActionSource],
      'extra': instance.extra,
      'extraType': _$RoomMessageExtraTypeEnumMap[instance.extraType],
      'content': instance.content,
      'position': instance.position,
      'gift': instance.gift,
      'giftReceives': instance.giftReceives,
    };

const _$RoomMessageEventEnumMap = {
  RoomMessageEvent.newMessage: 'newMessage',
  RoomMessageEvent.systemMessage: 'systemMessage',
  RoomMessageEvent.userStatus: 'userStatus',
  RoomMessageEvent.followCreator: 'followCreator',
  RoomMessageEvent.giftMessage: 'giftMessage',
  RoomMessageEvent.actionMessage: 'actionMessage',
  RoomMessageEvent.followBack: 'followBack',
};

const _$RoomMessageEventSubtypeEnumMap = {
  RoomMessageEventSubtype.joinRoom: 'joinRoom',
  RoomMessageEventSubtype.dropOnMic: 'dropOnMic',
  RoomMessageEventSubtype.quitManager: 'quitManager',
  RoomMessageEventSubtype.micRequest: 'micRequest',
  RoomMessageEventSubtype.agreeMicRequest: 'agreeMicRequest',
  RoomMessageEventSubtype.rejectMicRequest: 'rejectMicRequest',
  RoomMessageEventSubtype.inviteOnMic: 'inviteOnMic',
  RoomMessageEventSubtype.agreeInviteOnMic: 'agreeInviteOnMic',
  RoomMessageEventSubtype.rejectInviteOnMic: 'rejectInviteOnMic',
  RoomMessageEventSubtype.inviteManager: 'inviteManager',
  RoomMessageEventSubtype.rejectInviteManager: 'rejectInviteManager',
  RoomMessageEventSubtype.agreeInviteManager: 'agreeInviteManager',
  RoomMessageEventSubtype.removeManager: 'removeManager',
  RoomMessageEventSubtype.muteMic: 'muteMic',
  RoomMessageEventSubtype.unmuteMic: 'unmuteMic',
  RoomMessageEventSubtype.changePositionRequest: 'changePositionRequest',
  RoomMessageEventSubtype.levelup: 'levelup',
  RoomMessageEventSubtype.userMuteMic: 'userMuteMic',
  RoomMessageEventSubtype.userUnmuteMic: 'userUnmuteMic',
  RoomMessageEventSubtype.videoPlay: 'videoPlay',
  RoomMessageEventSubtype.videoPause: 'videoPause',
  RoomMessageEventSubtype.videoEnd: 'videoEnd',
  RoomMessageEventSubtype.videoSeek: 'videoSeek',
  RoomMessageEventSubtype.kickOut: 'kickOut',
};

const _$RoomMessageFollowActionSourceEnumMap = {
  RoomMessageFollowActionSource.followBack: 'followBack',
  RoomMessageFollowActionSource.giftSender: 'giftSender',
  RoomMessageFollowActionSource.giftReceiver: 'giftReceiver',
};

const _$RoomMessageExtraTypeEnumMap = {
  RoomMessageExtraType.followBack: 'followBack',
};
